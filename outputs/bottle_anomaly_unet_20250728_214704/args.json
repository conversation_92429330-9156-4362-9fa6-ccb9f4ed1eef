{"data_root": "../datasets/mvtec_anomaly_detection", "category": "bottle", "image_size": 256, "model": "anomaly_unet", "bilinear": false, "epochs": 2, "batch_size": 4, "learning_rate": 0.001, "weight_decay": 0.0001, "optimizer": "adam", "scheduler": "cosine", "recon_weight": 1.0, "seg_weight": 1.0, "use_ssim": false, "num_workers": 4, "device": "auto", "seed": 42, "save_dir": "../outputs", "save_freq": 1, "resume": null, "val_freq": 1}